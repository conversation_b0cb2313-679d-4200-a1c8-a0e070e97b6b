# LLM Tips Feature

This feature generates and posts contextual tips to Slack using various LLM providers, with built-in anti-repetition logic.

## Overview

The tips system:
- Uses configurable YAML prompts per category
- Supports multiple LLM adapters (Anthropic, OpenAI, Gemini)
- Stores canonical keys in SQLite to prevent semantic repeats
- Posts formatted tips to Slack channels
- Includes "chat-like" context by showing recent tips to the LLM

## Quick Start

1. **Set up environment variables:**
```bash
export SLACK_TOKEN="xoxb-your-slack-token"
export ANTHROPIC_API_KEY="sk-ant-your-key"  # Default adapter
# Optional: OPENAI_API_KEY, GEMINI_API_KEY
```

2. **Generate a tip:**
```bash
go run ./cmd/dev_prod_cli.go tips --category rails-performance
```

3. **Use different LLM or channel:**
```bash
go run ./cmd/dev_prod_cli.go tips \
  --category frontend-performance \
  --llm openai \
  --channel "#frontend-tips"
```

## Configuration

### System Prompts (config/tips_prompts.yaml)

Each category has its own system prompt:

```yaml
rails-performance:
  system_prompt: |
    You are a concise mentor for Rails performance optimization...

frontend-performance:
  system_prompt: |
    You are a concise mentor for frontend performance...
```

### Channel Mapping (Optional)

Set environment variables to map categories to channels:

```bash
export TIPS_CHANNEL_RAILS_PERFORMANCE="#rails-tips"
export TIPS_CHANNEL_FRONTEND_PERFORMANCE="#frontend-tips"
export TIPS_CHANNEL_LEARNING_DEVOPS="#devops-learning"
export TIPS_CHANNEL_LEARNING_AI="#ai-learning"
```

## CLI Usage

```bash
go run ./cmd/dev_prod_cli.go tips [OPTIONS]

Options:
  --category value           Category for the tip (required)
  --system-prompts-file      Path to YAML prompts file (default: config/tips_prompts.yaml)
  --llm value               LLM provider: anthropic, openai, gemini (default: anthropic)
  --channel value           Slack channel override
  --recentN value           Number of recent tips for context (default: 20)
```

## LLM Adapters

### Anthropic (Default)
- Model: `claude-3-5-sonnet-20241022` (configurable via `ANTHROPIC_MODEL`)
- Requires: `ANTHROPIC_API_KEY`

### OpenAI
- Model: `gpt-4o-mini` (configurable via `OPENAI_MODEL`)
- Requires: `OPENAI_API_KEY`

### Gemini
- Model: `gemini-1.5-pro` (configurable via `GEMINI_MODEL`)
- Requires: `GEMINI_API_KEY`

## Anti-Repetition Logic

1. **Canonical Keys**: Each tip gets a normalized "canonical_key" (e.g., "avoid-n-plus-one-queries")
2. **Database Uniqueness**: `UNIQUE(category, canonical_key)` constraint prevents exact duplicates
3. **Context Injection**: Recent canonical keys are included in LLM prompts
4. **Retry Logic**: If duplicate detected, retries up to 3 times

## Database Schema

Tips are stored in SQLite at `./db/dev-prod-app.db`:

```sql
CREATE TABLE tips (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  category TEXT NOT NULL,
  canonical_key TEXT NOT NULL,
  title TEXT,
  content TEXT NOT NULL,
  created_at TEXT NOT NULL,
  UNIQUE(category, canonical_key)
);
```

## Scheduling

Use cron or Kubernetes CronJobs to run tips automatically:

```bash
# Daily Rails tips at 9 AM
0 9 * * * /path/to/cli tips --category rails-performance

# Frontend tips on weekdays
0 10 * * 1-5 /path/to/cli tips --category frontend-performance --llm openai
```

## Example Output

```
✅ Successfully generated and posted tip:
   Category: rails-performance
   Canonical Key: use-counter-cache-for-associations
   Channel: #eng-learning
   LLM: anthropic
```

Slack message:
```
:bulb: **Optimize Association Counts with Counter Cache**

Use Rails counter_cache to avoid N+1 queries when displaying association counts...
```

## Troubleshooting

- **"category not found"**: Check `config/tips_prompts.yaml` has the category
- **"API key required"**: Set the appropriate `*_API_KEY` environment variable
- **"already exists"**: The system detected a duplicate canonical key and will retry
- **"failed to send to Slack"**: Check `SLACK_TOKEN` and channel permissions
