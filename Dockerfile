# Build stage
FROM golang:1.18-alpine AS builder
RUN apk add --no-cache gcc musl-dev

# Set the working directory
WORKDIR /app

# Copy the Go module and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the Go app
RUN go build -o server ./cmd/main.go
RUN go build -o cli ./cmd/dev_prod_cli.go

# Run stage
FROM alpine:latest

# Install build dependencies for SQLite
RUN apk add --no-cache sqlite

# Set the working directory
WORKDIR /app
# Create directory for database files
RUN mkdir -p /app/db

# Copy the built binary from the builder stage
COPY --from=builder /app/server .
COPY --from=builder /app/cli .
COPY .env /app/.env

# Copy configuration files, if any, for example:
# COPY config/config.yaml /app/config.yaml

# Expose the port Gin will run on
EXPOSE 3000

# Run the application
CMD ["./server"]
