# Binaries for programs and plugins
*.exe
*.dll
*.so
*.dylib

# Output of `go build`, `go run`
*.out
*.test

# Build directories
/build/
/bin/
/dist/

# Dependency directories (for vendoring)
vendor/

# Go test and coverage files
*.test
*.coverprofile
*.cover.out

# Logs and debug output
*.log
*.trace

# GoLand IDE
.idea/

# VS Code
.vscode/
.history/

# OS generated files
.DS_Store
Thumbs.db

# Air live-reload configuration (if you're using Air for live reload)
.air.toml
.air

# Temporary files created by tools like Fresh, Air, or CompileDaemon
tmp/