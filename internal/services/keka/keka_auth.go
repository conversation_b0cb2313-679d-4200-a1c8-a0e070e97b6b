package keka

import (
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"io/ioutil"
	"encoding/json"
)

const (
	tokenURL = "https://login.keka.com/connect/token"
)

type TokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

func GenerateKekaAccessToken() (string, error) {
	clientID := os.Getenv("KEKA_CLIENT_ID")
	clientSecret := os.Getenv("KEKA_CLIENT_SECRET")
	apiKey := os.Getenv("KEKA_API_KEY")

	if clientID == "" || clientSecret == "" || apiKey == "" {
		return "", fmt.Errorf("client ID, secret, and API key must be set in environment variables")
	}

	data := url.Values{}
	data.Set("grant_type", "kekaapi")
	data.Set("client_id", clientID)
	data.Set("client_secret", clientSecret)
	data.Set("api_key", apiKey)
	data.Set("scope", "kekaapi")

	req, err := http.NewRequest("POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return "", fmt.Errorf("failed to get token: %s", body)
	}

	var tokenResponse TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
		return "", err
	}

	if tokenResponse.AccessToken == "" {
		return "", nil
	}

	return tokenResponse.AccessToken, nil
}
