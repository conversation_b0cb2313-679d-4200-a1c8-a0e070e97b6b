// keka_leave_service.go
package keka

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"time"

	"github.com/slack-go/slack" // Import Slack package
)

const (
	apiURL = "https://peoplebox.keka.com/api/v1/time/leaverequests"
)

type LeaveRequest struct {
	ID                 string  `json:"id"`
	EmployeeIdentifier string  `json:"employeeIdentifier"`
	EmployeeNumber     string  `json:"employeeNumber"`
	FromDate           string  `json:"fromDate"`
	ToDate             string  `json:"toDate"`
	FromSession        int     `json:"fromSession"`
	ToSession          int     `json:"toSession"`
	RequestedOn        string  `json:"requestedOn"`
	Note               string  `json:"note"`
	Status             int     `json:"status"`
	LeaveReason        *string `json:"leaveReason"`        // Nullable field
	CancelRejectReason *string `json:"cancelRejectReason"` // Nullable field
	Selection          []struct {
		LeaveTypeIdentifier string  `json:"leaveTypeIdentifier"`
		LeaveTypeName       string  `json:"leaveTypeName"`
		Count               float64 `json:"count"`
		Duration            struct {
			Unit           int     `json:"unit"`
			Duration       float64 `json:"duration"`
			DurationString string  `json:"durationString"`
		} `json:"duration"`
	} `json:"selection"`
	LastActionTakenOn string `json:"lastActionTakenOn"`
	EmployeeName      string // New field for employee name
}

type LeaveResponse struct {
	Data []LeaveRequest `json:"data"`
	// Other fields can be added if needed
}

type Employee struct {
	ID             string `json:"id"`
	EmployeeNumber string `json:"employeeNumber"`
	FirstName      string `json:"firstName"`
	LastName       string `json:"lastName"`
	DisplayName    string `json:"displayName"`
}

type EmployeeResponse struct {
	Succeeded bool       `json:"succeeded"`
	Data      []Employee `json:"data"`
}

func FetchUpcomingLeaves(accessToken string) ([]LeaveRequest, error) {
	// Fetch employee data
	employees, err := FetchEmployees(accessToken)
	if err != nil {
		return nil, err
	}

	// Calculate the date range
	now := time.Now().UTC()
	from := now.Format(time.RFC3339)
	to := now.Add(70 * 24 * time.Hour).Format(time.RFC3339)

	// Update the API URL with the date range
	apiURLWithDates := fmt.Sprintf("%s?from=%s&to=%s", apiURL, from, to)

	req, err := http.NewRequest("GET", apiURLWithDates, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Accept", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to fetch leaves: %s, response body: %s", resp.Status, string(body))
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var leaveResponse LeaveResponse
	if err := json.Unmarshal(body, &leaveResponse); err != nil {
		return nil, err
	}

	// Add employee names to leave requests
	for i := range leaveResponse.Data {
		if employee, exists := employees[leaveResponse.Data[i].EmployeeNumber]; exists {
			leaveResponse.Data[i].EmployeeName = employee.DisplayName // Assuming you add EmployeeName field to LeaveRequest
		}
	}

	return leaveResponse.Data, nil
}

func FetchEmployees(accessToken string) (map[string]Employee, error) {
	req, err := http.NewRequest("GET", "https://peoplebox.keka.com/api/v1/hris/employees", nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Accept", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to fetch employees: %s, response body: %s", resp.Status, string(body))
	}

	var employeeResponse EmployeeResponse
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(body, &employeeResponse); err != nil {
		return nil, err
	}

	employeeMap := make(map[string]Employee)
	for _, employee := range employeeResponse.Data {
		employeeMap[employee.EmployeeNumber] = employee
	}

	return employeeMap, nil
}

// New function to fetch leaves and employees
func FetchLeavesAndEmployees() ([]LeaveRequest, map[string]Employee, error) {
	accessToken, err := GenerateKekaAccessToken()
	if err != nil {
		return nil, nil, err
	}

	// Fetch employee data
	employees, err := FetchEmployees(accessToken)
	if err != nil {
		return nil, nil, err
	}

	// Fetch upcoming leaves
	leaves, err := FetchUpcomingLeaves(accessToken)
	if err != nil {
		return nil, nil, err
	}

	return leaves, employees, nil
}

// New function to send notifications to Slack
func SendSlackNotification(message string) error {
	api := slack.New(os.Getenv("SLACK_TOKEN")) // Replace with your Slack bot token
	channelID := "#in-office"

	// Create a contextblock
	contextText := slack.NewTextBlockObject(slack.MarkdownType, "Use /apply-leave to apply leave", false, false)
	contextBlock := slack.NewContextBlock("context", contextText)

	// Create a text block with the message
	textBlock := slack.NewSectionBlock(&slack.TextBlockObject{Type: slack.MarkdownType, Text: message}, nil, nil)

	// Create the blocks
	blocks := slack.Blocks{
		BlockSet: []slack.Block{
			textBlock,
			contextBlock,
		},
	}

	// Create a section with the button
	_, _, err := api.PostMessage(channelID, slack.MsgOptionBlocks(blocks.BlockSet...))

	return err
}

// Call ScheduleNotifications in your main function or initialization
// ScheduleNotifications()
