package services

import (
    "encoding/json"
    "fmt"
    "net/http"
    "strings"
    "time"
    "dev-prod-app/config"
)

var (
    // Merged list of environments
    environments = []string{"analytics-beta", "auto", "alpha", "beta", "delta", "epsilon", "gamma", "test", "staging", "zeta"}

    // List of developers
    devs = []string{"<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>rp<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>aga<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"}

    backendRepo  = "Peoplebox/culturegrade-api"
    frontendRepo = "Peoplebox/culturegrade-dashboard"
)

// GetDevList returns a formatted list of developer names
func GetDevList() string {
    return strings.Join(devs, "\n")
}

// GetEnvironmentList returns a formatted list of development environments
func GetEnvironmentList() string {
    return strings.Join(environments, "\n")
}

// Additional functions (e.g., GetBranchInfo, GetLastCommitter, GenerateReport) remain as previously defined

// Example of GetBranchInfo for completeness
type BranchInfo struct {
    Backend  string `json:"backend"`
    Frontend string `json:"frontend"`
}

func GetBranchInfo(environment string) BranchInfo {
    backendURL := fmt.Sprintf("https://api.%s.peoplebox.ai/version.json", environment)
    frontendURL := fmt.Sprintf("https://dashboard.%s.peoplebox.ai/version.json", environment)

    backendVersion := getVersion(backendURL)
    frontendVersion := getVersion(frontendURL)

    return BranchInfo{Backend: backendVersion, Frontend: frontendVersion}
}

func getVersion(url string) string {
    client := http.Client{Timeout: 5 * time.Second}
    response, err := client.Get(url)
    if err != nil || response.StatusCode != http.StatusOK {
        return "Environment down"
    }
    defer response.Body.Close()

    var result map[string]string
    json.NewDecoder(response.Body).Decode(&result)
    return result["branch"]
}


// Format the relative date based on the commit timestamp
func formatRelativeDate(commitTime string) string {
    layout := "2006-01-02T15:04:05Z"
    utcTime, _ := time.Parse(layout, commitTime)
    istTime := utcTime.Add(5*time.Hour + 30*time.Minute)
    nowIST := time.Now().UTC().Add(5*time.Hour + 30*time.Minute)

    delta := nowIST.Sub(istTime)

    switch days := int(delta.Hours() / 24); {
    case days == 0:
        return "Today"
    case days == 1:
        return "Yesterday"
    case days < 4:
        return fmt.Sprintf("%d days ago", days)
    default:
        return istTime.Format("02 Jan")
    }
}

// GetLastCommitter fetches the latest committer details for a branch in a GitHub repository
func GetLastCommitter(repo, branchName string) (string, string) {
    url := fmt.Sprintf("https://api.github.com/repos/%s/commits?sha=%s&per_page=1", repo, branchName)
    req, _ := http.NewRequest("GET", url, nil)
    req.Header.Set("Authorization", "token "+config.GitHubToken) // Use config package for GitHub token

    response, err := http.DefaultClient.Do(req)
    if err != nil {
        return "Unknown", "Unknown"
    }
    defer response.Body.Close()

    var commits []map[string]interface{}
    json.NewDecoder(response.Body).Decode(&commits)
    if len(commits) > 0 {
        commitInfo := commits[0]["commit"].(map[string]interface{})
        authorName := commitInfo["author"].(map[string]interface{})["name"].(string)
        commitTime := commitInfo["author"].(map[string]interface{})["date"].(string)
        formattedTime := formatRelativeDate(commitTime)
        return authorName, formattedTime
    }
    return "Unknown", "Unknown"
}

// GenerateReport creates the deployment information report for Slack
func GenerateReport(userName string) string {
    report := fmt.Sprintf("*Deployment Info requested by @%s*\n\n", userName)

    for _, env := range environments {
        branchInfo := GetBranchInfo(env)
        report += fmt.Sprintf("*%s:*\n", env)

        // Backend information
        if branchInfo.Backend == "Environment down" {
            report += fmt.Sprintf("- Backend: `%s`\n", branchInfo.Backend)
        } else {
            backendCommitter, backendCommitTime := GetLastCommitter(backendRepo, branchInfo.Backend)
            if backendCommitTime == "Today" || backendCommitTime == "Yesterday" || backendCommitTime == "days ago" {
                report += fmt.Sprintf("- Backend: `%s` by `%s` %s\n", branchInfo.Backend, backendCommitter, backendCommitTime)
            } else {
                report += fmt.Sprintf("- Backend: `%s` by `%s` on `%s`\n", branchInfo.Backend, backendCommitter, backendCommitTime)
            }
        }

        // Frontend information
        if branchInfo.Frontend == "Environment down" {
            report += fmt.Sprintf("- Frontend: `%s`\n", branchInfo.Frontend)
        } else {
            frontendCommitter, frontendCommitTime := GetLastCommitter(frontendRepo, branchInfo.Frontend)
            if frontendCommitTime == "Today" || frontendCommitTime == "Yesterday" || frontendCommitTime == "days ago" {
                report += fmt.Sprintf("- Frontend: `%s` by `%s` %s\n", branchInfo.Frontend, frontendCommitter, frontendCommitTime)
            } else {
                report += fmt.Sprintf("- Frontend: `%s` by `%s` on `%s`\n", branchInfo.Frontend, frontendCommitter, frontendCommitTime)
            }
        }
        report += "\n"
    }
    return report
}

// SendEnvInfoToSlack generates the report and sends it to Slack
func SendEnvInfoToSlack(data map[string][]string) {
    channelName := data["channel_name"][0]
    userName := data["user_name"][0]
    report := GenerateReport(userName)
    SendSlackMessage(report, channelName)
}
