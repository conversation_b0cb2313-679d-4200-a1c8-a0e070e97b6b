package services

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "dev-prod-app/config"
)

// SlackMessage represents the payload for a Slack message
type SlackMessage struct {
    Channel string       `json:"channel"`
    Text    string       `json:"text"`
    Blocks  []SlackBlock `json:"blocks,omitempty"`
}

// Slack<PERSON>lock represents a block in the Slack message
type SlackBlock struct {
    Type     string        `json:"type"`
    Text     *SlackText    `json:"text,omitempty"`
    Elements []SlackElement `json:"elements,omitempty"`
}

// SlackText represents the text content in a Slack block
type SlackText struct {
    Type string `json:"type"`
    Text string `json:"text"`
}

// SlackElement represents elements within context blocks in Slack
type SlackElement struct {
    Type string `json:"type"`
    Text string `json:"text"`
}

// SendSlackMessage sends a formatted message to a specified Slack channel
func SendSlackMessage(message, channel string) error {
    url := "https://slack.com/api/chat.postMessage"

    // Construct the message payload
    payload := SlackMessage{
        Channel: channel,
        Text:    message,
        Blocks: []SlackBlock{
            {
                Type: "section",
                Text: &SlackText{
                    Type: "mrkdwn",
                    Text: message,
                },
            },
            {
                Type: "context",
                Elements: []SlackElement{
                    {
                        Type: "mrkdwn",
                        Text: "Use /env-info command to get this message",
                    },
                },
            },
        },
    }

    // Encode the payload as JSON
    jsonPayload, err := json.Marshal(payload)
    if err != nil {
        return fmt.Errorf("failed to marshal JSON payload: %v", err)
    }

    // Create a new HTTP request with the Slack API URL and payload
    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
    if err != nil {
        return fmt.Errorf("failed to create HTTP request: %v", err)
    }

    // Set headers for the Slack API request
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+config.SlackToken)

    // Send the HTTP request
    client := &http.Client{}
    response, err := client.Do(req)
    if err != nil {
        return fmt.Errorf("error sending message to Slack: %v", err)
    }
    defer response.Body.Close()

    // Check if the response status is OK
    if response.StatusCode != http.StatusOK {
        return fmt.Errorf("failed to send message to Slack: %s", response.Status)
    }

    return nil
}