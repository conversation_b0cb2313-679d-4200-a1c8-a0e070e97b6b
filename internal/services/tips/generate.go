package tips

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"dev-prod-app/internal/services"
)

// SelectAdapter returns the appropriate LLM adapter based on name
func SelectAdapter(name string) LLMAdapter {
	switch strings.ToLower(name) {
	case "", "anthropic", "claude":
		return NewAnthropicAdapter()
	case "openai":
		return NewOpenAIAdapter()
	case "gemini", "google":
		return NewGeminiAdapter()
	default:
		return NewAnthropicAdapter() // Default to Anthropic
	}
}

// GenerateAndPostTip orchestrates the entire tip generation and posting process
func GenerateAndPostTip(ctx context.Context, db *sql.DB, category, channel string,
	adapter LLMAdapter, sysPrompt string, recentN int) error {

	// Get recent canonical keys to avoid repeats
	pastKeys, err := GetRecentCanonicalKeys(ctx, db, category, recentN)
	if err != nil {
		return fmt.Errorf("failed to get recent keys: %v", err)
	}

	// Generate tip using LLM
	title, canonicalKey, content, err := adapter.GenerateTip(ctx, sysPrompt, category, pastKeys)
	if err != nil {
		return fmt.Errorf("failed to generate tip: %v", err)
	}

	// Ensure canonical key is normalized
	canonicalKey = NormalizeCanonicalKey(canonicalKey)
	if canonicalKey == "" {
		canonicalKey = NormalizeCanonicalKey(title)
	}

	// Create tip object
	tip := Tip{
		Category:     category,
		CanonicalKey: canonicalKey,
		Title:        title,
		Content:      content,
		CreatedAt:    time.Now().UTC().Format(time.RFC3339),
	}

	// Save to database
	if err := SaveTip(ctx, db, tip); err != nil {
		// Check if it's a uniqueness constraint violation
		if strings.Contains(err.Error(), "UNIQUE constraint failed") {
			return fmt.Errorf("tip with canonical key '%s' already exists for category '%s'. Try running again for a different tip", canonicalKey, category)
		}
		return fmt.Errorf("failed to save tip: %v", err)
	}

	// Format message for Slack
	msg := fmt.Sprintf(":bulb: **%s**\n\n%s", title, content)

	// Post to Slack using existing helper
	if err := services.SendSlackMessage(msg, channel); err != nil {
		return fmt.Errorf("failed to send to Slack: %v", err)
	}

	fmt.Printf("✅ Successfully generated and posted tip:\n")
	fmt.Printf("   Category: %s\n", category)
	fmt.Printf("   Canonical Key: %s\n", canonicalKey)
	fmt.Printf("   Channel: %s\n", channel)
	fmt.Printf("   LLM: %s\n", adapter.Name())

	return nil
}

// ValidateCategory checks if a category exists in the prompts
func ValidateCategory(prompts PromptsYAML, category string) error {
	if _, exists := prompts[category]; !exists {
		available := GetAvailableCategories(prompts)
		return fmt.Errorf("category '%s' not found. Available categories: %s", 
			category, strings.Join(available, ", "))
	}
	return nil
}

// RetryGenerateAndPostTip attempts to generate a tip with retry logic for uniqueness conflicts
func RetryGenerateAndPostTip(ctx context.Context, db *sql.DB, category, channel string,
	adapter LLMAdapter, sysPrompt string, recentN int, maxRetries int) error {

	var lastErr error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := GenerateAndPostTip(ctx, db, category, channel, adapter, sysPrompt, recentN)
		if err == nil {
			return nil // Success
		}

		lastErr = err

		// If it's a uniqueness constraint, try again
		if strings.Contains(err.Error(), "already exists") {
			fmt.Printf("⚠️  Attempt %d failed due to duplicate canonical key, retrying...\n", attempt)
			continue
		}

		// For other errors, don't retry
		return err
	}

	return fmt.Errorf("failed after %d attempts: %v", maxRetries, lastErr)
}
