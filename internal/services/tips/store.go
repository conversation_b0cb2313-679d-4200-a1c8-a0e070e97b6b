package tips

import (
	"context"
	"database/sql"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

const (
	dbPath = "./db/dev-prod-app.db" // Reuse existing DB path
)

type Tip struct {
	Category     string
	CanonicalKey string
	Title        string
	Content      string
	CreatedAt    string // RFC3339
}

const createTipsTable = `
CREATE TABLE IF NOT EXISTS tips (
	id INTEGER PRIMARY KEY AUTOINCREMENT,
	category TEXT NOT NULL,
	canonical_key TEXT NOT NULL,
	title TEXT,
	content TEXT NOT NULL,
	created_at TEXT NOT NULL,
	UNIQUE(category, canonical_key)
);`

// OpenDB opens the SQLite database connection
func OpenDB() (*sql.DB, error) {
	return sql.Open("sqlite3", dbPath)
}

// EnsureSchema creates the tips table if it doesn't exist
func EnsureSchema(db *sql.DB) error {
	_, err := db.Exec(createTipsTable)
	return err
}

// GetRecentCanonicalKeys fetches the last N canonical keys for a category
func GetRecentCanonicalKeys(ctx context.Context, db *sql.DB, category string, n int) ([]string, error) {
	query := `
		SELECT canonical_key 
		FROM tips 
		WHERE category = ? 
		ORDER BY created_at DESC 
		LIMIT ?`

	rows, err := db.QueryContext(ctx, query, category, n)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var keys []string
	for rows.Next() {
		var key string
		if err := rows.Scan(&key); err != nil {
			return nil, err
		}
		keys = append(keys, key)
	}

	return keys, rows.Err()
}

// SaveTip saves a new tip to the database
func SaveTip(ctx context.Context, db *sql.DB, t Tip) error {
	query := `
		INSERT INTO tips (category, canonical_key, title, content, created_at)
		VALUES (?, ?, ?, ?, ?)`

	_, err := db.ExecContext(ctx, query, t.Category, t.CanonicalKey, t.Title, t.Content, t.CreatedAt)
	return err
}

// GetAllTips fetches all tips for a category (for debugging)
func GetAllTips(ctx context.Context, db *sql.DB, category string) ([]Tip, error) {
	query := `
		SELECT category, canonical_key, title, content, created_at
		FROM tips 
		WHERE category = ? 
		ORDER BY created_at DESC`

	rows, err := db.QueryContext(ctx, query, category)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tips []Tip
	for rows.Next() {
		var tip Tip
		if err := rows.Scan(&tip.Category, &tip.CanonicalKey, &tip.Title, &tip.Content, &tip.CreatedAt); err != nil {
			return nil, err
		}
		tips = append(tips, tip)
	}

	return tips, rows.Err()
}
