package tips

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
)

type GeminiAdapter struct {
	apiKey string
	model  string
}

type GeminiRequest struct {
	Contents []GeminiContent `json:"contents"`
}

type GeminiContent struct {
	Role  string       `json:"role"`
	Parts []GeminiPart `json:"parts"`
}

type GeminiPart struct {
	Text string `json:"text"`
}

type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
}

type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

// NewGeminiAdapter creates a new Gemini adapter
func NewGeminiAdapter() *GeminiAdapter {
	model := os.Getenv("GEMINI_MODEL")
	if model == "" {
		model = "gemini-1.5-pro"
	}

	return &GeminiAdapter{
		apiKey: os.Getenv("GEMINI_API_KEY"),
		model:  model,
	}
}

func (a *GeminiAdapter) Name() string {
	return "gemini"
}

func (a *GeminiAdapter) GenerateTip(ctx context.Context, systemPrompt string, category string, pastKeys []string) (title, canonicalKey, content string, err error) {
	if a.apiKey == "" {
		return "", "", "", fmt.Errorf("GEMINI_API_KEY environment variable is required")
	}

	userPrompt := BuildUserPrompt(category, pastKeys)
	combinedPrompt := systemPrompt + "\n\n" + userPrompt

	req := GeminiRequest{
		Contents: []GeminiContent{
			{
				Role: "user",
				Parts: []GeminiPart{
					{Text: combinedPrompt},
				},
			},
		},
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	url := fmt.Sprintf("https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s", a.model, a.apiKey)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", "", "", fmt.Errorf("failed to create request: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return "", "", "", fmt.Errorf("API error %d: %s", resp.StatusCode, string(body))
	}

	var geminiResp GeminiResponse
	if err := json.NewDecoder(resp.Body).Decode(&geminiResp); err != nil {
		return "", "", "", fmt.Errorf("failed to decode response: %v", err)
	}

	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", "", "", fmt.Errorf("no content in response")
	}

	tipJSON, err := ParseTipJSON(geminiResp.Candidates[0].Content.Parts[0].Text)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to parse tip JSON: %v", err)
	}

	return tipJSON.Title, tipJSON.CanonicalKey, tipJSON.Content, nil
}
