package tips

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

// LLMAdapter interface for different LLM providers
type LLMAdapter interface {
	Name() string
	GenerateTip(ctx context.Context, systemPrompt string, category string, pastKeys []string) (title, canonicalKey, content string, err error)
}

// TipJSON represents the expected JSON response from LLMs
type TipJSON struct {
	Title        string `json:"title"`
	CanonicalKey string `json:"canonical_key"`
	Content      string `json:"content"`
}

// BuildUserPrompt constructs the user message with category and past keys
func BuildUserPrompt(category string, pastKeys []string) string {
	var b strings.Builder
	b.WriteString(fmt.Sprintf("Category: %s\n\n", category))

	if len(pastKeys) > 0 {
		b.WriteString("Past canonical keys to avoid:\n")
		for _, k := range pastKeys {
			b.WriteString("- " + k + "\n")
		}
		b.WriteString("\n")
	}

	b.WriteString("Produce a new tip that does not overlap any above.\n")
	b.WriteString("Return ONLY compact JSON with keys: title, canonical_key, content.")
	return b.String()
}

// Common stop words to remove from canonical keys
var stopWords = map[string]bool{
	"a": true, "an": true, "the": true, "to": true, "of": true,
	"for": true, "and": true, "or": true, "but": true, "in": true,
	"on": true, "at": true, "by": true, "with": true, "from": true,
}

// NormalizeCanonicalKey normalizes a canonical key to kebab-case
func NormalizeCanonicalKey(s string) string {
	if s == "" {
		return ""
	}

	s = strings.ToLower(s)
	// Replace non-alphanumeric with space
	re := regexp.MustCompile(`[^a-z0-9]+`)
	s = re.ReplaceAllString(s, " ")

	// Split into words and filter stop words
	fields := strings.Fields(s)
	out := make([]string, 0, len(fields))
	for _, w := range fields {
		if !stopWords[w] {
			out = append(out, w)
		}
	}

	// If all words were stop words, keep original
	if len(out) == 0 {
		out = fields
	}

	key := strings.Join(out, "-")

	// Limit length
	if len(key) > 70 {
		key = key[:70]
		key = strings.TrimRight(key, "-")
	}

	return key
}

// ParseTipJSON parses the JSON response from LLM
func ParseTipJSON(jsonStr string) (*TipJSON, error) {
	// Clean up the response - sometimes models add extra text
	jsonStr = strings.TrimSpace(jsonStr)
	
	// Find JSON object boundaries
	start := strings.Index(jsonStr, "{")
	end := strings.LastIndex(jsonStr, "}")
	
	if start == -1 || end == -1 || start >= end {
		return nil, fmt.Errorf("no valid JSON object found in response")
	}
	
	jsonStr = jsonStr[start : end+1]

	var tip TipJSON
	if err := json.Unmarshal([]byte(jsonStr), &tip); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Validate required fields
	if tip.Title == "" {
		return nil, fmt.Errorf("title is required")
	}
	if tip.Content == "" {
		return nil, fmt.Errorf("content is required")
	}

	// Normalize canonical key
	if tip.CanonicalKey == "" {
		tip.CanonicalKey = NormalizeCanonicalKey(tip.Title)
	} else {
		tip.CanonicalKey = NormalizeCanonicalKey(tip.CanonicalKey)
	}

	return &tip, nil
}
