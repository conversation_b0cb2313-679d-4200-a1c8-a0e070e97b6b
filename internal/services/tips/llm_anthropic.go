package tips

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
)

type AnthropicAdapter struct {
	apiKey string
	model  string
}

type AnthropicRequest struct {
	Model     string              `json:"model"`
	MaxTokens int                 `json:"max_tokens"`
	Messages  []AnthropicMessage  `json:"messages"`
}

type AnthropicMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type AnthropicResponse struct {
	Content []AnthropicContent `json:"content"`
}

type AnthropicContent struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

// NewAnthropicAdapter creates a new Anthropic adapter
func NewAnthropicAdapter() *AnthropicAdapter {
	model := os.Getenv("ANTHROPIC_MODEL")
	if model == "" {
		model = "claude-3-5-sonnet-20241022"
	}

	return &AnthropicAdapter{
		apiKey: os.Getenv("ANTHROPIC_API_KEY"),
		model:  model,
	}
}

func (a *AnthropicAdapter) Name() string {
	return "anthropic"
}

func (a *AnthropicAdapter) GenerateTip(ctx context.Context, systemPrompt string, category string, pastKeys []string) (title, canonicalKey, content string, err error) {
	if a.apiKey == "" {
		return "", "", "", fmt.Errorf("ANTHROPIC_API_KEY environment variable is required")
	}

	userPrompt := BuildUserPrompt(category, pastKeys)

	req := AnthropicRequest{
		Model:     a.model,
		MaxTokens: 800,
		Messages: []AnthropicMessage{
			{Role: "user", Content: systemPrompt + "\n\n" + userPrompt},
		},
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", "https://api.anthropic.com/v1/messages", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", "", "", fmt.Errorf("failed to create request: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", a.apiKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return "", "", "", fmt.Errorf("API error %d: %s", resp.StatusCode, string(body))
	}

	var anthropicResp AnthropicResponse
	if err := json.NewDecoder(resp.Body).Decode(&anthropicResp); err != nil {
		return "", "", "", fmt.Errorf("failed to decode response: %v", err)
	}

	if len(anthropicResp.Content) == 0 {
		return "", "", "", fmt.Errorf("no content in response")
	}

	tipJSON, err := ParseTipJSON(anthropicResp.Content[0].Text)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to parse tip JSON: %v", err)
	}

	return tipJSON.Title, tipJSON.CanonicalKey, tipJSON.Content, nil
}
