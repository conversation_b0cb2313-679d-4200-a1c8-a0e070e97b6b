package tips

import (
	"fmt"
	"io/ioutil"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

type CategoryPrompt struct {
	SystemPrompt string `yaml:"system_prompt"`
}

type PromptsYAML map[string]CategoryPrompt

// LoadPromptsYAML loads system prompts from a YAML file
func LoadPromptsYAML(path string) (PromptsYAML, error) {
	if path == "" {
		path = "config/tips_prompts.yaml"
	}

	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Erro<PERSON>("failed to read prompts file %s: %v", path, err)
	}

	var prompts PromptsYAML
	if err := yaml.Unmarshal(data, &prompts); err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %v", err)
	}

	return prompts, nil
}

// GetSystemPrompt retrieves the system prompt for a category
func GetSystemPrompt(prompts PromptsYAML, category string) (string, error) {
	prompt, exists := prompts[category]
	if !exists {
		return "", fmt.Errorf("category '%s' not found in prompts file", category)
	}

	if prompt.SystemPrompt == "" {
		return "", fmt.Errorf("system_prompt is empty for category '%s'", category)
	}

	return prompt.SystemPrompt, nil
}

// ResolveChannel determines the Slack channel for a category
func ResolveChannel(category, override string) string {
	if override != "" {
		return override
	}

	// Try environment variable: TIPS_CHANNEL_RAILS_PERFORMANCE
	envKey := "TIPS_CHANNEL_" + strings.ToUpper(strings.ReplaceAll(category, "-", "_"))
	if ch := os.Getenv(envKey); ch != "" {
		return ch
	}

	// Default fallback
	return "#eng-learning"
}

// GetAvailableCategories returns all available categories from the prompts
func GetAvailableCategories(prompts PromptsYAML) []string {
	var categories []string
	for category := range prompts {
		categories = append(categories, category)
	}
	return categories
}
