package tips

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
)

type OpenAIAdapter struct {
	apiKey string
	model  string
}

type OpenAIRequest struct {
	Model       string           `json:"model"`
	Messages    []OpenAIMessage  `json:"messages"`
	Temperature float64          `json:"temperature"`
	MaxTokens   int              `json:"max_tokens"`
}

type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIResponse struct {
	Choices []OpenAIChoice `json:"choices"`
}

type OpenAIChoice struct {
	Message OpenAIMessage `json:"message"`
}

// NewOpenAIAdapter creates a new OpenAI adapter
func NewOpenAIAdapter() *OpenAIAdapter {
	model := os.Getenv("OPENAI_MODEL")
	if model == "" {
		model = "gpt-4o-mini"
	}

	return &OpenAIAdapter{
		apiKey: os.Getenv("OPENAI_API_KEY"),
		model:  model,
	}
}

func (a *OpenAIAdapter) Name() string {
	return "openai"
}

func (a *OpenAIAdapter) GenerateTip(ctx context.Context, systemPrompt string, category string, pastKeys []string) (title, canonicalKey, content string, err error) {
	if a.apiKey == "" {
		return "", "", "", fmt.Errorf("OPENAI_API_KEY environment variable is required")
	}

	userPrompt := BuildUserPrompt(category, pastKeys)

	req := OpenAIRequest{
		Model:       a.model,
		Temperature: 0.4,
		MaxTokens:   800,
		Messages: []OpenAIMessage{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", "", "", fmt.Errorf("failed to create request: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+a.apiKey)

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return "", "", "", fmt.Errorf("API error %d: %s", resp.StatusCode, string(body))
	}

	var openaiResp OpenAIResponse
	if err := json.NewDecoder(resp.Body).Decode(&openaiResp); err != nil {
		return "", "", "", fmt.Errorf("failed to decode response: %v", err)
	}

	if len(openaiResp.Choices) == 0 {
		return "", "", "", fmt.Errorf("no choices in response")
	}

	tipJSON, err := ParseTipJSON(openaiResp.Choices[0].Message.Content)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to parse tip JSON: %v", err)
	}

	return tipJSON.Title, tipJSON.CanonicalKey, tipJSON.Content, nil
}
