package routes

import (
	"database/sql"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/mattn/go-sqlite3" // SQLite driver
)

const (
	dbPath = "./db/dev-prod-app.db" // Shared database path for branches
)

// GitHubCreateEvent represents the GitHub webhook payload for branch creation
type GitHubCreateEvent struct {
	Ref     string `json:"ref"`
	RefType string `json:"ref_type"`
	Repo    struct {
		Name     string `json:"name"`
		FullName string `json:"full_name"`
	} `json:"repository"`
}

// GitHubBranch represents a branch record for the webhook handler
type GitHubBranch struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	Repo      string    `json:"repo"`
	CreatedAt time.Time `json:"created_at"`
}

// Initialize database and create table if it doesn't exist
func initDB() (*sql.DB, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	// Create branches table if it doesn't exist
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS branches (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		repo TEXT NOT NULL,
		created_at TEXT NOT NULL
	);`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		return nil, err
	}

	return db, nil
}

// GitHub event header constants
const (
	githubEventHeader = "X-GitHub-Event"
	createEvent       = "create"
)

func RegisterGithubWebHooks(r *gin.Engine) {
	// Initialize the database when the app starts
	db, err := initDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	r.POST("/github_webhooks/save_branch", saveBranch)
}

func saveBranch(c *gin.Context) {

	// Check if this is a 'create' event
	eventType := c.GetHeader(githubEventHeader)
	if eventType != createEvent {
		c.JSON(http.StatusOK, gin.H{"message": "Ignored non-create event"})
		return
	}

	// Parse the GitHub webhook payload
	var event GitHubCreateEvent
	if err := c.ShouldBindJSON(&event); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload", "details": err.Error()})
		return
	}

	// We only want to process branch creations
	if event.RefType != "branch" {
		c.JSON(http.StatusOK, gin.H{"message": "Ignored non-branch event"})
		return
	}

	// Open database connection
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to connect to database", "details": err.Error()})
		return
	}
	defer db.Close()

	// Prepare branch data
	now := time.Now().UTC()
	timeStr := now.Format(time.RFC3339) // Store as ISO8601/RFC3339 format

	// Extract repository name from full name (e.g., "owner/repo" -> "repo")
	repoName := event.Repo.Name

	// Insert branch data into the database
	result, err := db.Exec(
		"INSERT INTO branches (name, repo, created_at) VALUES (?, ?, ?)",
		event.Ref, repoName, timeStr,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save branch", "details": err.Error()})
		return
	}

	// Get the inserted ID
	id, err := result.LastInsertId()
	if err != nil {
		log.Printf("Warning: Could not get last insert ID: %v", err)
	}

	// Create branch object for response
	branch := GitHubBranch{
		ID:        id,
		Name:      event.Ref,
		Repo:      repoName,
		CreatedAt: now,
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"message": "Branch saved successfully",
		"branch":  branch,
	})
}
