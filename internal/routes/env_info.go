package routes

import (
    "github.com/gin-gonic/gin"
    "net/http"
    "dev-prod-app/internal/services"
)

// RegisterEnvInfoRoutes registers routes related to environment and developer information
func RegisterEnvInfoRoutes(r *gin.Engine) {
    r.GET("/", rootHandler)
    r.GET("/devs", listDevsHandler)
    r.GET("/dev-environments", listDevEnvironmentsHandler)
    r.POST("/webhook/env_info", envInfoHandler)
}

// root handler to handle the "/" GET path
func rootHandler(c *gin.Context) {
    c.String(http.StatusOK, "🚀 Welcome to the Peoplebox Dev Productivity App – because real devs deploy on Fridays! 😉")
}

// listDevsHandler handles the `/devs` GET request
func listDevsHandler(c *gin.Context) {
    devList := services.GetDevList()
    c.String(http.StatusOK, devList)
}

// listDevEnvironmentsHandler handles the `/dev-environments` GET request
func listDevEnvironmentsHandler(c *gin.Context) {
    envList := services.GetEnvironmentList()
    c.String(http.StatusOK, envList)
}

// envInfoHandler handles the `/webhook/env_info` POST request
func envInfoHandler(c *gin.Context) {
    userName := c.PostForm("user_name")
    channelName := c.PostForm("channel_name")

    data := map[string][]string{
        "user_name":    {userName},
        "channel_name": {channelName},
    }

    go services.SendEnvInfoToSlack(data)

    c.JSON(http.StatusOK, gin.H{
        "response_type": "ephemeral",
        "text":          "Fetching deployment information, please wait...",
    })
}