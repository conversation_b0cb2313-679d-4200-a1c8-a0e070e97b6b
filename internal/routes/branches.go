package routes

import (
	"database/sql"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/mattn/go-sqlite3" // SQLite driver
)

// Branch represents a record in our branches table
type Branch struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	Repo      string    `json:"repo"`
	CreatedAt time.Time `json:"created_at"`
}

// RegisterBranchRoutes registers all branch-related routes
func RegisterBranchRoutes(r *gin.Engine) {
	r.GET("/deployments/branches/:repoName", listBranchesByRepo)
}

// listBranchesByRepo handles the GET request to list branches for a specific repository
// It returns branches in descending order by creation date (newest first)
func listBranchesByRepo(c *gin.Context) {
	// Get repo name from path parameter
	repoName := c.Param("repoName")
	if repoName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Repository name is required"})
		return
	}

	// Open database connection
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		log.Printf("Database connection error: %v", err)
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to connect to database", "details": err.Error()})
		return
	}
	defer db.Close()

	// Query branches for the specified repository (exact match)
	rows, err := db.Query(
		"SELECT id, name, repo, created_at FROM branches WHERE repo = ? ORDER BY created_at DESC",
		repoName,
	)
	if err != nil {
		log.Printf("Database query error for repo %s: %v", repoName, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query branches", "details": err.Error()})
		return
	}
	defer rows.Close()

	// Collect all branches from query results
	branches, err := scanBranches(rows)
	if err != nil {
		log.Printf("Error scanning branch data: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process branch data", "details": err.Error()})
		return
	}

	// Return branches as JSON response
	c.JSON(http.StatusOK, gin.H{
		"branches": branches,
	})
}

// scanBranches scans rows from the database query into Branch structs
// It handles the conversion of string timestamps to proper time.Time objects
func scanBranches(rows *sql.Rows) ([]Branch, error) {
	var branches []Branch
	for rows.Next() {
		var branch Branch
		var createdAtStr string

		// Scan the data into variables
		if err := rows.Scan(&branch.ID, &branch.Name, &branch.Repo, &createdAtStr); err != nil {
			return nil, err
		}

		// Parse the time string
		createdAt, err := time.Parse(time.RFC3339, createdAtStr)
		if err != nil {
			return nil, err
		}

		branch.CreatedAt = createdAt
		branches = append(branches, branch)
	}

	// Check for errors from iterating over rows
	if err := rows.Err(); err != nil {
		return nil, err
	}

	return branches, nil
}
