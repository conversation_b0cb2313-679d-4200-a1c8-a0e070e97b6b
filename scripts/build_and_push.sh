#!/bin/bash

# Get the latest Git commit hash
GIT_COMMIT_HASH=$(git rev-parse --short=20 HEAD)

# Define the Docker image name
export IMAGE_NAME="dev-prod-app"
export IMAGE="dev-prod-app"
export GAR_ASIA_SOUTH_REGION=asia-south1-docker.pkg.dev
export GAR_ASIA_SOUTH_PROJECT_ID=peoplebox-shared-services
export GAR_REPO_NAME=peoplebox-apps

# Build the Docker image with the latest Git commit hash as the tag
docker build -t $IMAGE_NAME:$GIT_COMMIT_HASH .

# Optionally, tag the image with 'latest'
docker tag $IMAGE_NAME:$GIT_COMMIT_HASH $IMAGE_NAME:latest

echo "Docker image $IMAGE_NAME:$GIT_COMMIT_HASH built and tagged as 'latest'"

# Login to Google Cloud Artifact Registry
echo -n "Logging into GCP Asia South Artifact Registry......."
gcloud auth activate-service-account --key-file=/home/<USER>/deployments/gcp-keys/peoplebox-shared-services-200406a011d4.json
gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin $GAR_ASIA_SOUTH_REGION
gcloud auth configure-docker $GAR_ASIA_SOUTH_REGION --quiet &>> /tmp/deploy.log

# Tag the image for the GAR repository
export NEXT_SHA=`git rev-parse --short=20 HEAD`
docker tag $IMAGE":latest" $GAR_ASIA_SOUTH_REGION/$GAR_ASIA_SOUTH_PROJECT_ID/$GAR_REPO_NAME/$IMAGE:$NEXT_SHA

# Push the Docker image to the Google Artifact Registry
echo -n "Pushing Docker Image ($GAR_ASIA_SOUTH_REGION/$GAR_ASIA_SOUTH_PROJECT_ID/$GAR_REPO_NAME/$IMAGE:$NEXT_SHA)......."
docker push $GAR_ASIA_SOUTH_REGION/$GAR_ASIA_SOUTH_PROJECT_ID/$GAR_REPO_NAME/$IMAGE:$NEXT_SHA &>> /tmp/deploy.log

# Update GitOps repository with the new image tag
echo -n "Updating gitops repository .......... "
export K8S_PATH=/tmp/pb-k8s-dev-prod
rm -rf $K8S_PATH
<NAME_EMAIL>:Peoplebox/peoplebox-k8s.git --branch main --single-branch --depth 1 $K8S_PATH
cd $K8S_PATH

# Update the Kubernetes deployment YAML with the new image tag
kust_file='devops/dev-prod-app/kustomization.yaml'
next_line_no=7
sed -E -i "${next_line_no}s/[a-z0-9]{20}/$NEXT_SHA/" $kust_file

# Commit and push the update to the GitOps repository
author=$(git log --format='%an' | head -1 | tr -d '\n')
email=$(git log --format='%ae' | head -1 | tr -d '\n')
git config user.name "$author"
git config user.email "$email"
git commit -m "Release version to $NEXT_SHA" $kust_file 
git push origin main 
cd -
