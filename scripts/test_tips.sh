#!/bin/bash

# Test script for tips functionality
echo "Testing tips CLI implementation..."

# Check if required environment variables are set
echo "Checking environment variables..."
if [ -z "$ANTHROPIC_API_KEY" ]; then
    echo "⚠️  ANTHROPIC_API_KEY not set (default adapter)"
fi

if [ -z "$SLACK_TOKEN" ]; then
    echo "⚠️  SLACK_TOKEN not set (required for posting)"
fi

# Test building the CLI
echo "Building CLI..."
go build -o /tmp/tips_cli ./cmd/dev_prod_cli.go

if [ $? -eq 0 ]; then
    echo "✅ CLI built successfully"
else
    echo "❌ CLI build failed"
    exit 1
fi

# Test help command
echo "Testing help command..."
/tmp/tips_cli tips --help

# Example usage (commented out to avoid actual API calls)
echo ""
echo "Example usage:"
echo "/tmp/tips_cli tips --category rails-performance --llm anthropic --channel '#eng-learning'"
echo "/tmp/tips_cli tips --category frontend-performance --llm openai"
echo "/tmp/tips_cli tips --category learning-devops --llm gemini --recentN 15"

echo ""
echo "Available categories from config/tips_prompts.yaml:"
echo "- rails-performance"
echo "- frontend-performance" 
echo "- learning-devops"
echo "- learning-ai"

echo ""
echo "Required environment variables:"
echo "- SLACK_TOKEN (always required)"
echo "- ANTHROPIC_API_KEY (for anthropic adapter - default)"
echo "- OPENAI_API_KEY (for openai adapter)"
echo "- GEMINI_API_KEY (for gemini adapter)"

echo ""
echo "Optional environment variables for channel mapping:"
echo "- TIPS_CHANNEL_RAILS_PERFORMANCE"
echo "- TIPS_CHANNEL_FRONTEND_PERFORMANCE"
echo "- TIPS_CHANNEL_LEARNING_DEVOPS"
echo "- TIPS_CHANNEL_LEARNING_AI"
