// main.go
package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"dev-prod-app/internal/services/keka"
	"dev-prod-app/internal/services/tips"

	"github.com/joho/godotenv"
	"github.com/urfave/cli/v2"
)

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		fmt.Println("Error loading .env file")
		return
	}

	app := &cli.App{
		Name:  "Service CLI",
		Usage: "A CLI to manage various services",
		Commands: []*cli.Command{
			{
				Name:  "keka",
				Usage: "Fetch upcoming leave requests from Keka API",
				Action: func(c *cli.Context) error {
					apiKey := os.Getenv("KEKA_API_KEY")
					if apiKey == "" {
						return fmt.Errorf("please set the KEKA_API_KEY environment variable")
					}

					leaves, _, err := keka.FetchLeavesAndEmployees()
					if err != nil {
						return fmt.Errorf("error fetching leaves: %v", err)
					}

					fmt.Println("Upcoming Leave Requests:")
					for _, leave := range leaves {
						fmt.Printf("ID: %s, Employee:%s, From Date: %s, To Date: %s, Status: %d\n", leave.ID, leave.EmployeeName, leave.FromDate, leave.ToDate, leave.Status)
					}
					return nil
				},
			},
			{
				Name:  "notify-leaves-next-week",
				Usage: "Notify upcoming leaves for next week",
				Action: func(c *cli.Context) error {
					leaves, _, err := keka.FetchLeavesAndEmployees()
					if err != nil {
						return err
					}

					// Group leaves by employee
					employeeLeaves := make(map[string][]keka.LeaveRequest)
					for _, leave := range leaves {
						employeeLeaves[leave.EmployeeName] = append(employeeLeaves[leave.EmployeeName], leave)
					}

					message := ":calendar: *Team leaves for next week:*\n\n"
					now := time.Now()
					offset := int(time.Monday - now.Weekday())
					if offset <= 0 {
						offset += 7
					}
					nextMonday := now.AddDate(0, 0, offset)
					nextFriday := nextMonday.AddDate(0, 0, 4)

					for employee, leaves := range employeeLeaves {
						for _, leave := range leaves {
							fromDate, _ := time.Parse(time.RFC3339, leave.FromDate)
							toDate, _ := time.Parse(time.RFC3339, leave.ToDate)

							// Check if any part of the leave falls within next week
							if (toDate.After(nextMonday) || toDate.Equal(nextMonday)) && (fromDate.Before(nextFriday) || fromDate.Equal(nextFriday)) {
								if fromDate.Format("2006-01-02") == toDate.Format("2006-01-02") {
									message += fmt.Sprintf(":bust_in_silhouette: *%s* - %s\n", employee, fromDate.Format("2 Jan (Mon)"))
								} else {
									message += fmt.Sprintf(":bust_in_silhouette: *%s* - %s to %s\n", employee, fromDate.Format("2 Jan (Mon)"), toDate.Format("2 Jan (Mon)"))
								}
								message += "\n"
							}
						}
					}

					return keka.SendSlackNotification(message)
				},
			},
			{
				Name:  "notify-leaves-today",
				Usage: "Notify team members on leave today",
				Action: func(c *cli.Context) error {
					leaves, _, err := keka.FetchLeavesAndEmployees()
					if err == nil {
						message := ":calendar: *Team members on leave today:*\n\n"
						today := time.Now().Format("2006-01-02")
						// Group leaves by employee
						employeeLeaves := make(map[string][]keka.LeaveRequest)
						for _, leave := range leaves {
							if leave.FromDate <= today && leave.ToDate >= today {
								employeeLeaves[leave.EmployeeName] = append(employeeLeaves[leave.EmployeeName], leave)
							}
						}

						// If no one is on leave, don't send a message
						if len(employeeLeaves) == 0 {
							return nil
						}

						for employee, _ := range employeeLeaves {
							message += fmt.Sprintf(":bust_in_silhouette: *%s*\n", employee)
							message += "\n"
						}

						return keka.SendSlackNotification(message)
					}
					return err
				},
				{
					Name:  "tips",
					Usage: "Generate and post a category tip using LLM",
					Flags: []cli.Flag{
						&cli.StringFlag{
							Name:     "category",
							Usage:    "Category for the tip (must match key in YAML file)",
							Required: true,
						},
						&cli.StringFlag{
							Name:  "system-prompts-file",
							Usage: "Path to YAML file with system prompts",
							Value: "config/tips_prompts.yaml",
						},
						&cli.StringFlag{
							Name:  "llm",
							Usage: "LLM provider to use (anthropic, openai, gemini)",
							Value: "anthropic",
						},
						&cli.StringFlag{
							Name:  "channel",
							Usage: "Slack channel to post to (optional, uses env mapping or default)",
						},
						&cli.IntFlag{
							Name:  "recentN",
							Usage: "Number of recent tips to include in context",
							Value: 20,
						},
					},
					Action: func(c *cli.Context) error {
						category := c.String("category")
						promptsFile := c.String("system-prompts-file")
						llmName := c.String("llm")
						channel := c.String("channel")
						recentN := c.Int("recentN")

						// Load prompts from YAML
						prompts, err := tips.LoadPromptsYAML(promptsFile)
						if err != nil {
							return fmt.Errorf("failed to load prompts: %v", err)
						}

						// Validate category exists
						if err := tips.ValidateCategory(prompts, category); err != nil {
							return err
						}

						// Get system prompt for category
						sysPrompt, err := tips.GetSystemPrompt(prompts, category)
						if err != nil {
							return err
						}

						// Resolve channel
						if channel == "" {
							channel = tips.ResolveChannel(category, "")
						}

						// Open database
						db, err := tips.OpenDB()
						if err != nil {
							return fmt.Errorf("failed to open database: %v", err)
						}
						defer db.Close()

						// Ensure schema exists
						if err := tips.EnsureSchema(db); err != nil {
							return fmt.Errorf("failed to ensure schema: %v", err)
						}

						// Select LLM adapter
						adapter := tips.SelectAdapter(llmName)

						// Generate and post tip with retry logic
						ctx := context.Background()
						return tips.RetryGenerateAndPostTip(ctx, db, category, channel, adapter, sysPrompt, recentN, 3)
					},
				},
			},
		},
	}

	// Run the CLI application
	err = app.Run(os.Args)
	if err != nil {
		fmt.Println(err)
	}
}
