// main.go
package main

import (
	"fmt"
	"os"
	"time"

	"dev-prod-app/internal/services/keka"

	"github.com/joho/godotenv"
	"github.com/urfave/cli/v2"
)

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		fmt.Println("Error loading .env file")
		return
	}

	app := &cli.App{
		Name:  "Service CLI",
		Usage: "A CLI to manage various services",
		Commands: []*cli.Command{
			{
				Name:  "keka",
				Usage: "Fetch upcoming leave requests from Keka API",
				Action: func(c *cli.Context) error {
					apiKey := os.Getenv("KEKA_API_KEY")
					if apiKey == "" {
						return fmt.Errorf("please set the KEKA_API_KEY environment variable")
					}

					leaves, _, err := keka.FetchLeavesAndEmployees()
					if err != nil {
						return fmt.Errorf("error fetching leaves: %v", err)
					}

					fmt.Println("Upcoming Leave Requests:")
					for _, leave := range leaves {
						fmt.Printf("ID: %s, Employee:%s, From Date: %s, To Date: %s, Status: %d\n", leave.ID, leave.EmployeeName, leave.FromDate, leave.ToDate, leave.Status)
					}
					return nil
				},
			},
			{
				Name:  "notify-leaves-next-week",
				Usage: "Notify upcoming leaves for next week",
				Action: func(c *cli.Context) error {
					leaves, _, err := keka.FetchLeavesAndEmployees()
					if err != nil {
						return err
					}

					// Group leaves by employee
					employeeLeaves := make(map[string][]keka.LeaveRequest)
					for _, leave := range leaves {
						employeeLeaves[leave.EmployeeName] = append(employeeLeaves[leave.EmployeeName], leave)
					}

					message := ":calendar: *Team leaves for next week:*\n\n"
					now := time.Now()
					offset := int(time.Monday - now.Weekday())
					if offset <= 0 {
						offset += 7
					}
					nextMonday := now.AddDate(0, 0, offset)
					nextFriday := nextMonday.AddDate(0, 0, 4)

					for employee, leaves := range employeeLeaves {
						for _, leave := range leaves {
							fromDate, _ := time.Parse(time.RFC3339, leave.FromDate)
							toDate, _ := time.Parse(time.RFC3339, leave.ToDate)

							// Check if any part of the leave falls within next week
							if (toDate.After(nextMonday) || toDate.Equal(nextMonday)) && (fromDate.Before(nextFriday) || fromDate.Equal(nextFriday)) {
								if fromDate.Format("2006-01-02") == toDate.Format("2006-01-02") {
									message += fmt.Sprintf(":bust_in_silhouette: *%s* - %s\n", employee, fromDate.Format("2 Jan (Mon)"))
								} else {
									message += fmt.Sprintf(":bust_in_silhouette: *%s* - %s to %s\n", employee, fromDate.Format("2 Jan (Mon)"), toDate.Format("2 Jan (Mon)"))
								}
								message += "\n"
							}
						}
					}

					return keka.SendSlackNotification(message)
				},
			},
			{
				Name:  "notify-leaves-today",
				Usage: "Notify team members on leave today",
				Action: func(c *cli.Context) error {
					leaves, _, err := keka.FetchLeavesAndEmployees()
					if err == nil {
						message := ":calendar: *Team members on leave today:*\n\n"
						today := time.Now().Format("2006-01-02")
						// Group leaves by employee
						employeeLeaves := make(map[string][]keka.LeaveRequest)
						for _, leave := range leaves {
							if leave.FromDate <= today && leave.ToDate >= today {
								employeeLeaves[leave.EmployeeName] = append(employeeLeaves[leave.EmployeeName], leave)
							}
						}

						// If no one is on leave, don't send a message
						if len(employeeLeaves) == 0 {
							return nil
						}

						for employee, _ := range employeeLeaves {
							message += fmt.Sprintf(":bust_in_silhouette: *%s*\n", employee)
							message += "\n"
						}

						return keka.SendSlackNotification(message)
					}
					return err
				},
			},
		},
	}

	// Run the CLI application
	err = app.Run(os.Args)
	if err != nil {
		fmt.Println(err)
	}
}
