package main

import (
	"dev-prod-app/config"
	"dev-prod-app/internal/routes"
	"log"
	"net/http"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Attempt to load .env file, but ignore error if it doesn’t exist
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Fatalf("Error loading .env file: %v", err)
		}
	} else {
		log.Println("No .env file found; using system environment variables")
	}

	// Load configuration from environment variables
	config.LoadConfig()

	// Initialize Gin router
	router := gin.Default()

	// Configure CORS middleware
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{"https://drone.devops.peoplebox.ai"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	corsConfig.AllowCredentials = true
	corsConfig.MaxAge = 12 * 60 * 60
	router.Use(cors.New(corsConfig))

	// Add OPTIONS handler for preflight requests
	router.OPTIONS("/*path", func(c *gin.Context) {
		c.Status(http.StatusOK)
	})

	// Register routes
	routes.RegisterEnvInfoRoutes(router)
	routes.RegisterGithubWebHooks(router)
	routes.RegisterBranchRoutes(router)

	// Start the server
	log.Println("Server running on port 3000")
	router.Run(":3000")
}
